import { Data } from "src/lib/darkcloud/angular/dsadmin";
import { IdPrefixes } from "src/lib/darkcloud/dialogue-system";
import { Base } from "./Base";

export class Custom
extends Base<Data.Hard.ICustom, Data.Result.ICustom>
implements Required<Data.Hard.ICustom>
{
    public static generateId(index: number)
    {
      return IdPrefixes.CUSTOM + index;
    }

    constructor(
      index: number,
      dataAccess: Custom['TDataAccess']
    )
    {
      super({hard: {id: Custom.generateId(index)}}, dataAccess);
    }

    public get gateXPclassItem()
    {
      return this.hard.gateXPclassItem;
    }

    public set gateXPclassItem(value: string)
    {
      this.hard.gateXPclassItem = value;
    }

    public get weaponClassItem()
    {
      return this.hard.weaponClassItem;
    }

    public set weaponClassItem(value: string[])
    {
      this.hard.weaponClassItem = value;
    }

    public get commonWeaponClassItem()
    {
      return this.hard.commonWeaponClassItem;
    }

    public set commonWeaponClassItem(value: string[])
    {
      this.hard.commonWeaponClassItem = value;
    }

    public get particleClassItem()
    {
      return this.hard.particleClassItem;
    }

    public set particleClassItem(value: string[])
    {
      this.hard.particleClassItem = value;
    }

    public get particlesOrder()
    {
      return this.hard.particlesOrder;
    }

    public set particlesOrder(value: string[])
    {
      this.hard.particlesOrder = value;
    }

    public get selectedWeaponId()
    {
      return this.hard.selectedWeaponId;
    }

    public set selectedWeaponId(value: string)
    {
      this.hard.selectedWeaponId = value;
    }


    public get upgradesClassItem()
    {
      return this.hard.upgradesClassItem;
    }

    public set upgradesClassItem(value: string[])
    {
      this.hard.upgradesClassItem = value;
    }

   /*  public get selectedCharacterId()
    {
      return this.hard.selectedCharacterId;
    }

    public set selectedCharacterId(value: string)
    {
      this.hard.selectedCharacterId = value;
    } */

    public get selectedParticleId()
    {
      return this.hard.selectedParticleId;
    }

    public set selectedParticleId(value: string)
    {
      this.hard.selectedParticleId = value;
    }

    public get generatedPaths()
    {
      return this.hard.generatedPaths;
    }

    public set generatedPaths(value: boolean)
    {
      this.hard.generatedPaths = value;
    }

    public get powerupClassItem()
    {
      return this.hard.powerupClassItem;
    }

    public set powerupClassItem(value: string[])
    {
      this.hard.powerupClassItem = value;
    }

    public get selectedPowerupId()
    {
      return this.hard.selectedPowerupId;
    }

    public set selectedPowerupId(value: string)
    {
      this.hard.selectedPowerupId = value;
    }

    public get silicatesClassItem()
    {
      return this.hard.silicatesClassItem;
    }

    public set silicatesClassItem(value: string[])
    {
      this.hard.silicatesClassItem = value;
    }


    public get selectedSilicateId()
    {
      return this.hard.selectedSilicateId;
    }

    public set selectedSilicateId(value: string)
    {
      this.hard.selectedSilicateId = value;
    }


    public get memoryModuleClassItem()
    {
      return this.hard.memoryModuleClassItem;
    }

    public set memoryModuleClassItem(value: string[])
    {
      this.hard.memoryModuleClassItem = value;
    }

    public get selectedMemoryModuleId()
    {
      return this.hard.selectedMemoryModuleId;
    }

    public set selectedMemoryModuleId(value: string)
    {
      this.hard.selectedMemoryModuleId = value;
    }

    public get specialWeaponClassItem()
    {
      return this.hard.specialWeaponClassItem;
    }

    public set specialWeaponClassItem(value: string[])
    {
      this.hard.specialWeaponClassItem = value;
    }

    public get blueprintClassItem()
    {
      return this.hard.blueprintClassItem;
    }

    public set blueprintClassItem(value: string[])
    {
      this.hard.blueprintClassItem = value;
    }

    public get selectedSpecialWeaponId()
    {
      return this.hard.selectedSpecialWeaponId;
    }

    public set selectedSpecialWeaponId(value: string)
    {
      this.hard.selectedSpecialWeaponId = value;
    }
}
