
.class-selection-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 20px;
}

.class-selection-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  align-items: stretch;
}

.class-chip {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 20px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
  border: 2px solid #dee2e6;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  font-size: 16px;
  color: #495057;
  min-height: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  user-select: none;
  position: relative;
  overflow: hidden;
}

.class-chip::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.class-chip:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #adb5bd;
  background: linear-gradient(135deg, #ffffff 0%, #e9ecef 100%);
}

.class-chip:hover::before {
  left: 100%;
}

.class-chip:active {
  transform: translateY(0);
  transition: transform 0.1s;
}

.class-chip.selected {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border-color: #28a745;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.class-chip.selected:hover {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  border-color: #1e7e34;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.class-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}
