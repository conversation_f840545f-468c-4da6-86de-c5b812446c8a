import { IBase } from ".";

export interface ICustom extends IBase
{
    gateXPclassItem?: string;
    weaponClassItem?: string[];
    commonWeaponClassItem?: string[];
    particleClassItem?: string[];
    selectedWeaponId?: string;
    particlesOrder?: string[];
    selectedParticleId?: string;
    powerupClassItem?: string[];
    selectedPowerupId?: string;
    memoryModuleClassItem?: string[];
    selectedMemoryModuleId?: string;
    specialWeaponClassItem?: string[];
    blueprintClassItem?: string[];
    upgradesClassItem?: string[];
    selectedSpecialWeaponId?: string;
    silicatesClassItem?: string[];
    selectedSilicateId?: string;
    //selectedCharacterId?: string;

    generatedPaths?: boolean;
}
